# 小程序 WebView 键盘兼容性问题解决方案

## 问题描述

在小程序中（特别是快手小程序和安卓设备），当用户进入 WebView 页面并加载 H5 表单页面时，如果在输入框中输入内容导致键盘弹起，然后点击左上角返回按钮回到原生页面，键盘可能不会自动收起，影响用户体验。

**影响范围：**
- 快手小程序（所有设备）
- 安卓设备（所有小程序平台）
- 部分 iOS 设备（偶发）

## 解决方案

### 1. 创建键盘处理 Hook

创建了 `src/hooks/useKeyboardHandler/index.ts`，提供统一的键盘收起处理逻辑：

**主要功能：**
- 页面隐藏时自动收起键盘
- 页面卸载时自动收起键盘
- 针对快手小程序的特殊处理
- 支持延迟收起键盘
- 监听页面可见性变化

**使用方式：**
```typescript
import useKeyboardHandler from '@/hooks/useKeyboardHandler'

// 在组件中使用
useKeyboardHandler({
  onPageHide: true,    // 页面隐藏时收起键盘
  onPageUnload: true,  // 页面卸载时收起键盘
  delay: 0,           // 延迟时间（毫秒）
})
```

### 2. 修改 WebView 页面

在 `src/subpackage/web-view/index.tsx` 中集成键盘处理 Hook：

**修改内容：**
- 引入 `useKeyboardHandler` Hook
- 配置在页面隐藏和卸载时自动收起键盘
- 移除了自定义导航栏方案（因为 WebView 会覆盖导航栏）

### 3. WebView 键盘工具函数

创建了 `src/utils/webview-keyboard.ts`，提供专门的 WebView 键盘处理：

**主要功能：**
- 强制收起键盘（多平台兼容）
- WebView 消息监听
- H5 页面注入脚本
- 设备兼容性检查

### 4. 兼容性处理

**安卓设备特殊处理：**
- 多次尝试收起键盘（50ms、100ms、200ms 间隔）
- 监听 `focusout` 事件
- 触发页面重新布局
- 定时检查键盘状态

**快手小程序特殊处理：**
- 使用 `ks.hideKeyboard()` API
- 监听返回按钮事件（如果支持）
- 监听页面可见性变化事件

**通用处理：**
- 使用 `$.taro.hideKeyboard()` API
- 失焦当前活动元素
- 页面生命周期钩子处理
- WebView 消息通信

## 技术实现细节

### 键盘收起逻辑

```typescript
const hideKeyboard = useCallback(() => {
  const executeHide = () => {
    // 快手小程序处理
    if (process.env.TARO_ENV === 'kwai') {
      if (typeof ks !== 'undefined' && ks.hideKeyboard) {
        ks.hideKeyboard()
      }
    }

    // 通用处理
    if ($.taro.hideKeyboard) {
      $.taro.hideKeyboard()
    }

    // 额外兼容性处理
    if (typeof document !== 'undefined' && document.activeElement) {
      const activeElement = document.activeElement as HTMLElement
      if (activeElement && typeof activeElement.blur === 'function') {
        activeElement.blur()
      }
    }
  }

  if (delay > 0) {
    setTimeout(executeHide, delay)
  } else {
    executeHide()
  }
}, [delay])
```

### 生命周期处理

- **useDidHide**: 页面隐藏时触发
- **useUnload**: 页面卸载时触发
- **visibilitychange**: 页面可见性变化时触发

## 使用建议

1. **在所有 WebView 页面中使用**：建议在所有可能包含表单输入的 WebView 页面中使用此 Hook
2. **根据需要调整延迟**：如果发现键盘收起过快，可以适当增加 delay 参数
3. **测试不同机型**：在不同的快手小程序版本和设备上进行测试

## 注意事项

1. **WebView 限制**：WebView 组件会覆盖整个页面，无法使用自定义导航栏
2. **API 兼容性**：不同小程序平台的键盘 API 可能有差异
3. **性能考虑**：避免频繁调用键盘收起 API

## 测试验证

建议在以下场景进行测试：
1. 快手小程序真机环境
2. 包含表单输入的 H5 页面
3. 键盘弹起状态下点击返回按钮
4. 不同的设备型号和系统版本
