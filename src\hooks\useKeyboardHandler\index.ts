/*
 * @Date: 2025-01-12
 * @Description: 键盘处理 Hook，解决快手小程序键盘不收起的兼容性问题
 */

import { useEffect, useCallback } from 'react'
import { useDidHide, useUnload } from '@tarojs/taro'

/**
 * 键盘处理 Hook
 * 主要解决快手小程序中 WebView 页面键盘不自动收起的问题
 * 
 * @param options 配置选项
 * @param options.onPageHide 页面隐藏时是否收起键盘，默认 true
 * @param options.onPageUnload 页面卸载时是否收起键盘，默认 true
 * @param options.delay 收起键盘的延迟时间（毫秒），默认 0
 */
interface UseKeyboardHandlerOptions {
  onPageHide?: boolean
  onPageUnload?: boolean
  delay?: number
}

export default function useKeyboardHandler(options: UseKeyboardHandlerOptions = {}) {
  const {
    onPageHide = true,
    onPageUnload = true,
    delay = 0
  } = options

  // 强制收起键盘的通用方法
  const hideKeyboard = useCallback(() => {
    const executeHide = () => {
      // 针对快手小程序的键盘收起处理
      if (process.env.TARO_ENV === 'kwai') {
        // 检查快手小程序 API 是否可用
        if (typeof ks !== 'undefined' && ks.hideKeyboard) {
          ks.hideKeyboard()
        }
      }

      // 通用的键盘收起处理
      if ($.taro.hideKeyboard) {
        $.taro.hideKeyboard()
      }

      // 额外的兼容性处理：尝试失焦当前活动元素
      if (typeof document !== 'undefined' && document.activeElement) {
        const activeElement = document.activeElement as HTMLElement
        if (activeElement && typeof activeElement.blur === 'function') {
          activeElement.blur()
        }
      }
    }

    if (delay > 0) {
      setTimeout(executeHide, delay)
    } else {
      executeHide()
    }
  }, [delay])

  // 页面隐藏时强制收起键盘
  useDidHide(() => {
    if (onPageHide) {
      hideKeyboard()
    }
  })

  // 页面卸载时也进行键盘收起处理
  useUnload(() => {
    if (onPageUnload) {
      hideKeyboard()
    }
  })

  // 监听快手小程序的返回按钮事件和其他兼容性处理
  useEffect(() => {
    if (process.env.TARO_ENV === 'kwai') {
      // 尝试监听快手小程序的返回事件
      const handleBackButton = () => {
        hideKeyboard()
      }

      // 检查是否支持返回按钮监听
      if (typeof ks !== 'undefined' && ks.onBackPress) {
        ks.onBackPress(handleBackButton)

        return () => {
          // 清理监听器
          if (ks.offBackPress) {
            ks.offBackPress(handleBackButton)
          }
        }
      }

      // 额外的快手小程序兼容性处理
      // 监听页面可见性变化
      const handleVisibilityChange = () => {
        if (document.hidden) {
          hideKeyboard()
        }
      }

      if (typeof document !== 'undefined') {
        document.addEventListener('visibilitychange', handleVisibilityChange)

        return () => {
          document.removeEventListener('visibilitychange', handleVisibilityChange)
        }
      }
    }
  }, [hideKeyboard])

  return {
    hideKeyboard
  }
}
