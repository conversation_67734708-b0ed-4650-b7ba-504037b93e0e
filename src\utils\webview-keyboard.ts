/*
 * @Date: 2025-01-12
 * @Description: WebView 键盘处理工具函数
 */

/**
 * 强制收起键盘的工具函数
 * 针对不同平台和设备进行优化
 */
export const forceHideKeyboard = () => {
  const systemInfo = $.taro.getSystemInfoSync()
  const isAndroid = systemInfo.platform.toLowerCase().includes('android')
  const isIOS = systemInfo.platform.toLowerCase().includes('ios')

  // 立即执行第一次收起
  executeHideKeyboard()

  // 安卓设备需要多次尝试
  if (isAndroid) {
    setTimeout(() => executeHideKeyboard(), 50)
    setTimeout(() => executeHideKeyboard(), 100)
    setTimeout(() => executeHideKeyboard(), 200)
  }

  // iOS 设备延迟一次
  if (isIOS) {
    setTimeout(() => executeHideKeyboard(), 100)
  }
}

/**
 * 执行键盘收起的核心逻辑
 */
const executeHideKeyboard = () => {
  // 快手小程序 API
  if (process.env.TARO_ENV === 'kwai' && typeof ks !== 'undefined' && ks.hideKeyboard) {
    ks.hideKeyboard()
  }

  // Taro 通用 API
  if ($.taro.hideKeyboard) {
    $.taro.hideKeyboard()
  }

  // 失焦当前活动元素
  if (typeof document !== 'undefined' && document.activeElement) {
    const activeElement = document.activeElement as HTMLElement
    if (activeElement && typeof activeElement.blur === 'function') {
      activeElement.blur()
    }
  }

  // 尝试其他可能的键盘收起方法
  try {
    // 微信小程序
    if (typeof wx !== 'undefined' && wx.hideKeyboard) {
      wx.hideKeyboard()
    }

    // 支付宝小程序
    if (typeof my !== 'undefined' && my.hideKeyboard) {
      my.hideKeyboard()
    }

    // 字节跳动小程序
    if (typeof tt !== 'undefined' && tt.hideKeyboard) {
      tt.hideKeyboard()
    }
  } catch (error) {
    // 静默处理错误
  }
}

/**
 * 监听 WebView 消息，处理 H5 页面发送的键盘收起请求
 */
export const setupWebViewKeyboardListener = () => {
  const handleMessage = (event: any) => {
    try {
      const data = event.detail?.data?.[0] || event.detail
      if (data && data.type === 'hideKeyboard') {
        forceHideKeyboard()
      }
    } catch (error) {
      // 静默处理错误
    }
  }

  return handleMessage
}

/**
 * 为 H5 页面提供的 JavaScript 代码
 * 可以注入到 WebView 中，让 H5 页面主动通知小程序收起键盘
 */
export const getWebViewKeyboardScript = () => {
  return `
    (function() {
      // 监听输入框失焦事件
      document.addEventListener('focusout', function(e) {
        if (e.target && (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA')) {
          // 通知小程序收起键盘
          if (window.wx && window.wx.miniProgram) {
            window.wx.miniProgram.postMessage({
              data: { type: 'hideKeyboard' }
            });
          }
        }
      });

      // 监听页面隐藏事件
      document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
          if (window.wx && window.wx.miniProgram) {
            window.wx.miniProgram.postMessage({
              data: { type: 'hideKeyboard' }
            });
          }
        }
      });

      // 监听返回按钮（如果支持）
      window.addEventListener('popstate', function() {
        if (window.wx && window.wx.miniProgram) {
          window.wx.miniProgram.postMessage({
            data: { type: 'hideKeyboard' }
          });
        }
      });
    })();
  `
}

/**
 * 检查当前设备是否需要特殊的键盘处理
 */
export const needsKeyboardFix = () => {
  const systemInfo = $.taro.getSystemInfoSync()
  const isAndroid = systemInfo.platform.toLowerCase().includes('android')
  const isKwai = process.env.TARO_ENV === 'kwai'
  
  // 安卓设备或快手小程序都需要特殊处理
  return isAndroid || isKwai
}
